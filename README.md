# Family Med Manager

A React Native Expo app for managing family healthcare records, appointments, and medications.

## Features

- Welcome splash screen with smooth animations
- Clean, modern UI design
- Healthcare-focused branding

## Getting Started

### Prerequisites

- Node.js (v16 or later)
- npm or yarn
- Expo CLI

### Installation

1. Install dependencies:
   ```bash
   npm install
   ```

2. Install Expo CLI globally (if not already installed):
   ```bash
   npm install -g @expo/cli
   ```

### Running the App

1. Start the development server:
   ```bash
   npm start
   ```

2. Use the Expo Go app on your phone to scan the QR code, or:
   - Press `i` to open iOS simulator
   - Press `a` to open Android emulator
   - Press `w` to open in web browser

## Project Structure

```
├── App.js              # Main application component with splash screen
├── app.json            # Expo configuration
├── package.json        # Dependencies and scripts
├── babel.config.js     # Babel configuration
├── assets/             # Images and other assets
└── README.md           # This file
```

## Development

The app currently displays a welcome splash screen with:
- Animated fade-in effect
- Healthcare-themed design
- Welcome message and app branding

## Next Steps

- Add navigation structure
- Implement user authentication
- Create healthcare record management features
- Add appointment scheduling
- Implement medication tracking
