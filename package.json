{"name": "family-med-manager", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start --tunnel", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web --port 3000", "web-only": "npx serve -s web-build -l 3000"}, "dependencies": {"expo": "~54.0.0", "expo-splash-screen": "~0.28.0", "expo-status-bar": "~2.0.0", "react": "18.3.1", "react-native": "0.76.0", "react-dom": "18.3.1", "react-native-web": "~0.19.0"}, "devDependencies": {"@babel/core": "^7.25.0", "babel-preset-expo": "~11.0.0"}, "private": true}