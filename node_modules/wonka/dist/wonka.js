'use strict';

function __(a, b) {
  b.tag = a;
  return b;
}

function talkbackPlaceholder(a) {}

function fromArray(a) {
  return function (b) {
    var c = a.length;
    let e = !1,
      d = !1,
      f = !1,
      g = 0;
    b(
      __(0, [
        function (h) {
          if (h) {
            e = !0;
          } else if (d) {
            f = !0;
          } else {
            for (d = f = !0; f && !e; ) {
              g < c ? ((h = a[g]), (g = (g + 1) | 0), (f = !1), b(__(1, [h]))) : ((e = !0), b(0));
            }
            d = !1;
          }
        },
      ])
    );
  };
}

function _ref() {}

function _ref4(a) {
  a(0);
}

function empty(a) {
  let b = !1;
  a(
    __(0, [
      function (c) {
        c ? (b = !0) : b || a(0);
      },
    ])
  );
}

function some(a) {
  if (void 0 === a) {
    return ((a = [undefinedHeader, 0]).tag = 256), a;
  }
  if (null === a || a[0] !== undefinedHeader) {
    return a;
  }
  (a = [undefinedHeader, (a[1] + 1) | 0]).tag = 256;
  return a;
}

function valFromOption(a) {
  if (null === a || a[0] !== undefinedHeader) {
    return a;
  }
  if (0 !== (a = a[1])) {
    return [undefinedHeader, (a - 1) | 0];
  }
}

function concatMap(a) {
  return function (b) {
    return function (c) {
      function e(b) {
        'number' == typeof b
          ? k &&
            ((k = !1),
            void 0 !== (b = d.shift())
              ? ((b = a(valFromOption(b))), (k = !0), b(e))
              : m
              ? c(0)
              : g || ((g = !0), f(0)))
          : b.tag
          ? k && (c(b), l ? (l = !1) : h(0))
          : ((h = b = b[0]), (l = !1), b(0));
      }
      let d = [],
        f = talkbackPlaceholder,
        g = !1,
        h = talkbackPlaceholder,
        k = !1,
        l = !1,
        m = !1;
      b(function (b) {
        'number' == typeof b
          ? m ||
            ((m = !0),
            !k &&
              (function (a) {
                return 0 === a.length;
              })(d) &&
              c(0))
          : b.tag
          ? m || ((b = b[0]), (g = !1), k ? d.push(b) : ((b = a(b)), (k = !0), b(e)))
          : (f = b[0]);
      });
      c(
        __(0, [
          function (a) {
            if (a) {
              if ((m || ((m = !0), f(1)), k)) {
                return (k = !1), h(1);
              }
            } else {
              m || g || ((g = !0), f(0)), k && !l && ((l = !0), h(0));
            }
          },
        ])
      );
    };
  };
}

function _ref3(a) {
  return a;
}

function _ref4$1(a) {
  return a;
}

function _ref7(a) {
  return a(0);
}

function mergeMap(a) {
  return function (b) {
    return function (c) {
      let e = talkbackPlaceholder,
        d = !1,
        f = [],
        g = !1;
      b(function (b) {
        'number' == typeof b
          ? g || ((g = !0), 0 === f.length && c(0))
          : b.tag
          ? g ||
            ((d = !1),
            (function (a) {
              function b(a) {
                return a !== k;
              }
              var h = function (a) {
                'number' == typeof a
                  ? 0 !== f.length &&
                    ((f = f.filter(b)),
                    (a = 0 === f.length),
                    g && a ? c(0) : !d && a && ((d = !0), e(0)))
                  : a.tag
                  ? 0 !== f.length && (c(__(1, [a[0]])), k(0))
                  : ((k = a = a[0]), (f = f.concat(a)), a(0));
              };
              let k = talkbackPlaceholder;
              1 === a.length ? a(h) : a.bind(null, h);
            })(a(b[0])),
            d || ((d = !0), e(0)))
          : (e = b[0]);
      });
      c(
        __(0, [
          function (a) {
            a
              ? (g || ((g = !0), e(a)),
                f.forEach(function (c) {
                  return c(a);
                }),
                (f = []))
              : (d || g ? (d = !1) : ((d = !0), e(0)), f.forEach(_ref7));
          },
        ])
      );
    };
  };
}

function _ref8(a) {
  return a;
}

function _ref9(a) {
  return a;
}

function mergeAll(a) {
  return mergeMap(_ref9)(a);
}

function onPush(a) {
  return function (b) {
    return function (c) {
      let e = !1;
      return b(function (d) {
        if ('number' == typeof d) {
          e || ((e = !0), c(d));
        } else if (d.tag) {
          e || (a(d[0]), c(d));
        } else {
          var g = d[0];
          c(
            __(0, [
              function (a) {
                if (!e) {
                  return a && (e = !0), g(a);
                }
              },
            ])
          );
        }
      });
    };
  };
}

function _ref11(a) {
  a(0);
}

function switchMap(a) {
  return function (b) {
    return function (c) {
      function e(a) {
        h &&
          ('number' == typeof a
            ? ((h = !1), l ? c(a) : f || ((f = !0), d(0)))
            : a.tag
            ? (c(a), k ? (k = !1) : g(0))
            : ((g = a = a[0]), (k = !1), a(0)));
      }
      let d = talkbackPlaceholder,
        f = !1,
        g = talkbackPlaceholder,
        h = !1,
        k = !1,
        l = !1;
      b(function (b) {
        'number' == typeof b
          ? l || ((l = !0), h || c(0))
          : b.tag
          ? l ||
            (h && (g(1), (g = talkbackPlaceholder)),
            f ? (f = !1) : ((f = !0), d(0)),
            (b = a(b[0])),
            (h = !0),
            b(e))
          : (d = b[0]);
      });
      c(
        __(0, [
          function (a) {
            if (a) {
              if ((l || ((l = !0), d(1)), h)) {
                return (h = !1), g(1);
              }
            } else {
              l || f || ((f = !0), d(0)), h && !k && ((k = !0), g(0));
            }
          },
        ])
      );
    };
  };
}

function _ref16(a) {
  return a;
}

function takeLast(a) {
  return function (b) {
    return function (c) {
      let e = [],
        d = talkbackPlaceholder;
      return b(function (b) {
        'number' == typeof b
          ? fromArray(e)(c)
          : b.tag
          ? (e.length >= a && 0 < a && e.shift(), e.push(b[0]), d(0))
          : ((b = b[0]), 0 >= a ? (b(1), empty(c)) : ((d = b), b(0)));
      });
    };
  };
}

function subscribe(a) {
  return function (b) {
    let c = talkbackPlaceholder,
      e = !1;
    b(function (b) {
      'number' == typeof b ? (e = !0) : b.tag ? e || (a(b[0]), c(0)) : ((c = b = b[0]), b(0));
    });
    return {
      unsubscribe: function (a) {
        if (!e) {
          return (e = !0), c(1);
        }
      },
    };
  };
}

function _ref$1(a) {}

function _ref$2(a) {}

function _ref2() {}

function _ref3$1() {}

function _ref3$2(a, b, c) {
  a.addEventListener(b, c);
}

function _ref4$2(a, b, c) {
  a.removeEventListener(b, c);
}

var undefinedHeader,
  buffer$1,
  combine$1,
  concatMap$1,
  concatAll$1,
  concat$1,
  filter$1,
  map$1,
  mergeMap$1,
  merge$1,
  mergeAll$1,
  flatten$1,
  onEnd$1,
  onPush$1,
  tap$1,
  onStart$1,
  sample$1,
  scan$1,
  share$1,
  skip$1,
  skipUntil$1,
  skipWhile$1,
  switchMap$1,
  switchAll$1,
  take$1,
  takeLast$1,
  takeUntil$1,
  takeWhile$1,
  subscribe$1,
  forEach$1,
  publish$1,
  toArray$1,
  observableSymbol,
  fromObservable$2,
  toObservable$2,
  fromCallbag$2,
  toCallbag$2,
  debounce$1,
  delay$1,
  throttle$1,
  toPromise$1,
  interval$1,
  fromDomEvent$1,
  fromPromise$1,
  fromArray$1 = fromArray,
  empty$1 = empty;

undefinedHeader = [];

(buffer$1 = function (a) {
  return function (b) {
    return function (c) {
      function e(a) {
        'number' == typeof a
          ? k || ((k = !0), f(1), 0 < d.length && c(__(1, [d])), c(0))
          : a.tag
          ? !k && 0 < d.length && ((a = d), (d = []), c(__(1, [a])))
          : (g = a[0]);
      }
      let d = [],
        f = talkbackPlaceholder,
        g = talkbackPlaceholder,
        h = !1,
        k = !1;
      b(function (b) {
        'number' == typeof b
          ? k || ((k = !0), g(1), 0 < d.length && c(__(1, [d])), c(0))
          : b.tag
          ? k || (d.push(b[0]), h ? (h = !1) : ((h = !0), f(0), g(0)))
          : ((f = b[0]), a(e));
      });
      c(
        __(0, [
          function (c) {
            if (!k) {
              if (c) {
                return (k = !0), f(1), g(1);
              }
              if (!h) {
                return (h = !0), f(0), g(0);
              }
            }
          },
        ])
      );
    };
  };
}),
  (combine$1 = function (a, b) {
    return (function (a, b) {
      return function (c) {
        let e = talkbackPlaceholder,
          d = talkbackPlaceholder,
          f = void 0,
          g = void 0,
          h = !1,
          k = 0,
          l = !1;
        a(function (a) {
          var b = g;
          'number' == typeof a
            ? 1 > k
              ? (k = (k + 1) | 0)
              : l || ((l = !0), c(0))
            : a.tag
            ? ((a = a[0]),
              void 0 !== b
                ? l || ((f = some(a)), (h = !1), c(__(1, [[a, valFromOption(b)]])))
                : ((f = some(a)), h ? (h = !1) : d(0)))
            : (e = a[0]);
        });
        b(function (a) {
          var b = f;
          'number' == typeof a
            ? 1 > k
              ? (k = (k + 1) | 0)
              : l || ((l = !0), c(0))
            : a.tag
            ? ((a = a[0]),
              void 0 !== b
                ? l || ((g = some(a)), (h = !1), c(__(1, [[valFromOption(b), a]])))
                : ((g = some(a)), h ? (h = !1) : e(0)))
            : (d = a[0]);
        });
        c(
          __(0, [
            function (a) {
              if (!l) {
                if (a) {
                  return (l = !0), e(1), d(1);
                }
                if (!h) {
                  return (h = !0), e(a), d(a);
                }
              }
            },
          ])
        );
      };
    })(a, b);
  }),
  (concatMap$1 = concatMap),
  (concatAll$1 = function (a) {
    return concatMap(_ref3)(a);
  }),
  (concat$1 = function (a) {
    return concatMap(_ref4$1)(fromArray(a));
  }),
  (filter$1 = function (a) {
    return function (b) {
      return function (c) {
        let e = talkbackPlaceholder;
        return b(function (d) {
          'number' == typeof d ? c(d) : d.tag ? (a(d[0]) ? c(d) : e(0)) : ((e = d[0]), c(d));
        });
      };
    };
  }),
  (map$1 = function (a) {
    return function (b) {
      return function (c) {
        return b(function (b) {
          b = 'number' == typeof b ? 0 : b.tag ? __(1, [a(b[0])]) : __(0, [b[0]]);
          c(b);
        });
      };
    };
  }),
  (mergeMap$1 = mergeMap),
  (merge$1 = function (a) {
    return mergeMap(_ref8)(fromArray(a));
  }),
  (mergeAll$1 = mergeAll),
  (flatten$1 = mergeAll),
  (onEnd$1 = function (a) {
    return function (b) {
      return function (c) {
        let e = !1;
        return b(function (d) {
          if ('number' == typeof d) {
            if (e) {
              return;
            }
            e = !0;
            c(d);
            return a();
          }
          if (d.tag) {
            e || c(d);
          } else {
            var b = d[0];
            c(
              __(0, [
                function (c) {
                  if (!e) {
                    return c ? ((e = !0), b(c), a()) : b(c);
                  }
                },
              ])
            );
          }
        });
      };
    };
  }),
  (onPush$1 = onPush),
  (tap$1 = onPush),
  (onStart$1 = function (a) {
    return function (b) {
      return function (c) {
        return b(function (b) {
          'number' == typeof b ? c(b) : b.tag ? c(b) : (c(b), a());
        });
      };
    };
  }),
  (sample$1 = function (a) {
    return function (b) {
      return function (c) {
        let e = talkbackPlaceholder,
          d = talkbackPlaceholder,
          f = void 0,
          g = !1,
          h = !1;
        b(function (a) {
          'number' == typeof a
            ? h || ((h = !0), d(1), c(0))
            : a.tag
            ? ((f = some(a[0])), g ? (g = !1) : ((g = !0), d(0), e(0)))
            : (e = a[0]);
        });
        a(function (a) {
          var b = f;
          'number' == typeof a
            ? h || ((h = !0), e(1), c(0))
            : a.tag
            ? void 0 === b || h || ((f = void 0), c(__(1, [valFromOption(b)])))
            : (d = a[0]);
        });
        c(
          __(0, [
            function (a) {
              if (!h) {
                if (a) {
                  return (h = !0), e(1), d(1);
                }
                if (!g) {
                  return (g = !0), e(0), d(0);
                }
              }
            },
          ])
        );
      };
    };
  }),
  (scan$1 = function (a, b) {
    return (function (a, b) {
      return function (c) {
        return function (e) {
          let d = b;
          return c(function (c) {
            'number' == typeof c
              ? (c = 0)
              : c.tag
              ? ((d = a(d, c[0])), (c = __(1, [d])))
              : (c = __(0, [c[0]]));
            e(c);
          });
        };
      };
    })(a, b);
  }),
  (share$1 = function (a) {
    function b(a) {
      'number' == typeof a
        ? (c.forEach(_ref11), (c = []))
        : a.tag
        ? ((d = !1),
          c.forEach(function (c) {
            c(a);
          }))
        : (e = a[0]);
    }
    let c = [],
      e = talkbackPlaceholder,
      d = !1;
    return function (f) {
      function g(a) {
        return a !== f;
      }
      c = c.concat(f);
      1 === c.length && a(b);
      f(
        __(0, [
          function (a) {
            if (a) {
              if (((c = c.filter(g)), 0 === c.length)) {
                return e(1);
              }
            } else {
              d || ((d = !0), e(a));
            }
          },
        ])
      );
    };
  }),
  (skip$1 = function (a) {
    return function (b) {
      return function (c) {
        let e = talkbackPlaceholder,
          d = a;
        return b(function (a) {
          'number' == typeof a
            ? c(a)
            : a.tag
            ? 0 < d
              ? ((d = (d - 1) | 0), e(0))
              : c(a)
            : ((e = a[0]), c(a));
        });
      };
    };
  }),
  (skipUntil$1 = function (a) {
    return function (b) {
      return function (c) {
        function e(a) {
          'number' == typeof a
            ? g && ((k = !0), d(1))
            : a.tag
            ? ((g = !1), f(1))
            : ((f = a = a[0]), a(0));
        }
        let d = talkbackPlaceholder,
          f = talkbackPlaceholder,
          g = !0,
          h = !1,
          k = !1;
        b(function (b) {
          'number' == typeof b
            ? (g && f(1), (k = !0), c(0))
            : b.tag
            ? g || k
              ? h
                ? (h = !1)
                : ((h = !0), d(0), f(0))
              : ((h = !1), c(b))
            : ((d = b[0]), a(e));
        });
        c(
          __(0, [
            function (a) {
              if (!k) {
                if (a) {
                  if (((k = !0), d(1), g)) {
                    return f(1);
                  }
                } else {
                  h || ((h = !0), g && f(0), d(0));
                }
              }
            },
          ])
        );
      };
    };
  }),
  (skipWhile$1 = function (a) {
    return function (b) {
      return function (c) {
        let e = talkbackPlaceholder,
          d = !0;
        return b(function (b) {
          'number' == typeof b
            ? c(b)
            : b.tag
            ? d
              ? a(b[0])
                ? e(0)
                : ((d = !1), c(b))
              : c(b)
            : ((e = b[0]), c(b));
        });
      };
    };
  }),
  (switchMap$1 = switchMap),
  (switchAll$1 = function (a) {
    return switchMap(_ref16)(a);
  }),
  (take$1 = function (a) {
    return function (b) {
      return function (c) {
        let e = !1,
          d = 0,
          f = talkbackPlaceholder;
        b(function (b) {
          'number' == typeof b
            ? e || ((e = !0), c(0))
            : b.tag
            ? d < a && !e && ((d = (d + 1) | 0), c(b), !e && d >= a && ((e = !0), c(0), f(1)))
            : ((b = b[0]), 0 >= a ? ((e = !0), c(0), b(1)) : (f = b));
        });
        c(
          __(0, [
            function (b) {
              if (!e) {
                if (b) {
                  return (e = !0), f(1);
                }
                if (d < a) {
                  return f(0);
                }
              }
            },
          ])
        );
      };
    };
  }),
  (takeLast$1 = takeLast),
  (takeUntil$1 = function (a) {
    return function (b) {
      return function (c) {
        function e(a) {
          'number' != typeof a && (a.tag ? ((d = !0), f(1), c(0)) : ((g = a = a[0]), a(0)));
        }
        let d = !1,
          f = talkbackPlaceholder,
          g = talkbackPlaceholder;
        b(function (b) {
          'number' == typeof b
            ? d || ((d = !0), g(1), c(0))
            : b.tag
            ? d || c(b)
            : ((f = b[0]), a(e));
        });
        c(
          __(0, [
            function (a) {
              if (!d) {
                return a ? ((d = !0), f(1), g(1)) : f(0);
              }
            },
          ])
        );
      };
    };
  }),
  (takeWhile$1 = function (a) {
    return function (b) {
      return function (c) {
        let e = talkbackPlaceholder,
          d = !1;
        return b(function (b) {
          'number' == typeof b
            ? d || ((d = !0), c(0))
            : b.tag
            ? d || (a(b[0]) ? c(b) : ((d = !0), c(0), e(1)))
            : ((e = b[0]), c(b));
        });
      };
    };
  });

(subscribe$1 = subscribe),
  (forEach$1 = function (a) {
    return function (b) {
      subscribe(a)(b);
    };
  }),
  (publish$1 = function (a) {
    return subscribe(_ref$1)(a);
  }),
  (toArray$1 = function (a) {
    let b = [],
      c = talkbackPlaceholder,
      e = !1;
    a(function (a) {
      'number' == typeof a ? (e = !0) : a.tag ? (b.push(a[0]), c(0)) : ((c = a = a[0]), a(0));
    });
    e || c(1);
    return b;
  });

observableSymbol =
  'function' == typeof Symbol
    ? Symbol.observable || (Symbol.observable = Symbol('observable'))
    : '@@observable';

(fromObservable$2 = function (a) {
  var b = void 0 !== a[observableSymbol] ? a[observableSymbol]() : a;
  return function (a) {
    var c = b.subscribe({
      next: function (b) {
        a(__(1, [b]));
      },
      complete: function () {
        a(0);
      },
      error: _ref$2,
    });
    a(
      __(0, [
        function (a) {
          if (a) {
            return c.unsubscribe();
          }
        },
      ])
    );
  };
}),
  (toObservable$2 = function (a) {
    var b = {
      subscribe: function (b, e, d) {
        var c = ('object' == typeof b ? b.next.bind(b) : b) || _ref2,
          g = ('object' == typeof b ? b.complete.bind(b) : d) || _ref3$1;
        let h = talkbackPlaceholder,
          k = !1;
        a(function (a) {
          if ('number' == typeof a) {
            return (k = !0), g();
          }
          if (a.tag) {
            if (k) {
              return;
            }
            c(a[0]);
            return h(0);
          }
          h = a = a[0];
          a(0);
        });
        return {
          unsubscribe: function () {
            if (!k) {
              return (this.closed = !1), (k = !0), h(1);
            }
          },
          closed: !1,
        };
      },
    };
    b[observableSymbol] = function (a) {
      return b;
    };
    return b;
  }),
  (fromCallbag$2 = function (a) {
    return function (b) {
      var c = function (a, c) {
        switch (a) {
          case 0:
            b(
              __(0, [
                function (a) {
                  return a ? c(2) : c(1);
                },
              ])
            );
            break;

          case 1:
            b(__(1, [c]));
            break;

          case 2:
            b(0);
        }
      };
      return 2 === a.length ? a(0, c) : a.bind(null, 0, c);
    };
  }),
  (toCallbag$2 = function (a) {
    return function (b, c) {
      if (0 === b) {
        return a(function (a) {
          if ('number' == typeof a) {
            return 2 === c.length ? c(2, void 0) : c.bind(null, 2, void 0);
          }
          if (a.tag) {
            return (a = a[0]), 2 === c.length ? c(1, a) : c.bind(null, 1, a);
          }
          var b = a[0];
          a = function (a) {
            switch (a) {
              case 1:
                b(0);
                break;

              case 2:
                b(1);
            }
          };
          return 2 === c.length ? c(0, a) : c.bind(null, 0, a);
        });
      }
    };
  }),
  (debounce$1 = function (a) {
    return function (b) {
      return function (c) {
        let e = void 0,
          d = !1,
          f = !1;
        var g = function (a) {
          void 0 !== (a = e) && ((e = void 0), clearTimeout(valFromOption(a)));
        };
        return b(function (b) {
          if ('number' == typeof b) {
            f || ((f = !0), void 0 !== e ? (d = !0) : c(0));
          } else if (b.tag) {
            f ||
              (g(),
              (e = some(
                setTimeout(function (a) {
                  e = void 0;
                  c(b);
                  d && c(0);
                }, a(b[0]))
              )));
          } else {
            var l = b[0];
            c(
              __(0, [
                function (a) {
                  if (!f) {
                    return a ? ((f = !0), (d = !1), g(), l(1)) : l(0);
                  }
                },
              ])
            );
          }
        });
      };
    };
  }),
  (delay$1 = function (a) {
    return function (b) {
      return function (c) {
        let e = 0;
        return b(function (b) {
          'number' == typeof b || b.tag
            ? ((e = (e + 1) | 0),
              setTimeout(function (a) {
                0 !== e && ((e = (e - 1) | 0), c(b));
              }, a))
            : c(b);
        });
      };
    };
  }),
  (throttle$1 = function (a) {
    return function (b) {
      return function (c) {
        function e(a) {
          f = void 0;
          d = !1;
        }
        let d = !1,
          f = void 0;
        var g = function (a) {
          void 0 !== (a = f) && clearTimeout(valFromOption(a));
        };
        return b(function (b) {
          if ('number' == typeof b) {
            g(), c(0);
          } else if (b.tag) {
            d || ((d = !0), g(), (f = some(setTimeout(e, a(b[0])))), c(b));
          } else {
            var h = b[0];
            c(
              __(0, [
                function (a) {
                  return a ? (g(), h(1)) : h(a);
                },
              ])
            );
          }
        });
      };
    };
  }),
  (toPromise$1 = function (a) {
    return new Promise(function (b, c) {
      takeLast(1)(a)(function (a) {
        if ('number' != typeof a) {
          if (a.tag) {
            b(a[0]);
          } else {
            a[0](0);
          }
        }
      });
    });
  }),
  (interval$1 = function (a) {
    return function (b) {
      let c = 0;
      var e = setInterval(function (a) {
        a = c;
        c = (c + 1) | 0;
        b(__(1, [a]));
      }, a);
      b(
        __(0, [
          function (a) {
            a && clearInterval(e);
          },
        ])
      );
    };
  }),
  (fromDomEvent$1 = function (a, b) {
    return (function (a, b) {
      return function (c) {
        var e = _ref3$2,
          d = _ref4$2,
          f = function (a) {
            c(__(1, [a]));
          };
        c(
          __(0, [
            function (c) {
              if (c) {
                return d(a, b, f);
              }
            },
          ])
        );
        return e(a, b, f);
      };
    })(a, b);
  }),
  (fromPromise$1 = function (a) {
    return function (b) {
      let c = !1;
      a.then(function (a) {
        c || (b(__(1, [a])), b(0));
        return Promise.resolve(void 0);
      });
      b(
        __(0, [
          function (a) {
            a && (c = !0);
          },
        ])
      );
    };
  });

exports.buffer = buffer$1;

exports.combine = combine$1;

exports.concat = concat$1;

exports.concatAll = concatAll$1;

exports.concatMap = concatMap$1;

exports.debounce = debounce$1;

exports.delay = delay$1;

exports.empty = empty$1;

exports.filter = filter$1;

exports.flatten = flatten$1;

exports.forEach = forEach$1;

exports.fromArray = fromArray$1;

exports.fromCallbag = fromCallbag$2;

exports.fromDomEvent = fromDomEvent$1;

exports.fromList = function (a) {
  return function (b) {
    let c = !1,
      e = !1,
      d = !1,
      f = a;
    b(
      __(0, [
        function (a) {
          if (a) {
            c = !0;
          } else if (e) {
            d = !0;
          } else {
            for (e = d = !0; d && !c; ) {
              (a = f) ? ((f = a[1]), (d = !1), b(__(1, [a[0]]))) : ((c = !0), b(0));
            }
            e = !1;
          }
        },
      ])
    );
  };
};

exports.fromObservable = fromObservable$2;

exports.fromPromise = fromPromise$1;

exports.fromValue = function (a) {
  return function (b) {
    let c = !1;
    b(
      __(0, [
        function (e) {
          e ? (c = !0) : c || ((c = !0), b(__(1, [a])), b(0));
        },
      ])
    );
  };
};

exports.interval = interval$1;

exports.make = function (a) {
  return function (b) {
    let c = _ref,
      e = !1;
    c = a({
      next: function (d) {
        e || b(__(1, [d]));
      },
      complete: function (d) {
        e || ((e = !0), b(0));
      },
    });
    b(
      __(0, [
        function (d) {
          if (d && !e) {
            return (e = !0), c();
          }
        },
      ])
    );
  };
};

exports.makeSubject = function (a) {
  let b = [],
    c = !1;
  return {
    source: function (c) {
      function d(d) {
        return d !== c;
      }
      b = b.concat(c);
      c(
        __(0, [
          function (c) {
            c && (b = b.filter(d));
          },
        ])
      );
    },
    next: function (a) {
      c ||
        b.forEach(function (c) {
          c(__(1, [a]));
        });
    },
    complete: function (a) {
      c || ((c = !0), b.forEach(_ref4));
    },
  };
};

exports.map = map$1;

exports.merge = merge$1;

exports.mergeAll = mergeAll$1;

exports.mergeMap = mergeMap$1;

exports.never = function (a) {
  a(__(0, [talkbackPlaceholder]));
};

exports.onEnd = onEnd$1;

exports.onPush = onPush$1;

exports.onStart = onStart$1;

exports.pipe = function () {
  for (var a = arguments, b = arguments[0], c = 1, e = arguments.length; c < e; c++) {
    b = a[c](b);
  }
  return b;
};

exports.publish = publish$1;

exports.sample = sample$1;

exports.scan = scan$1;

exports.share = share$1;

exports.skip = skip$1;

exports.skipUntil = skipUntil$1;

exports.skipWhile = skipWhile$1;

exports.subscribe = subscribe$1;

exports.switchAll = switchAll$1;

exports.switchMap = switchMap$1;

exports.take = take$1;

exports.takeLast = takeLast$1;

exports.takeUntil = takeUntil$1;

exports.takeWhile = takeWhile$1;

exports.tap = tap$1;

exports.throttle = throttle$1;

exports.toArray = toArray$1;

exports.toCallbag = toCallbag$2;

exports.toObservable = toObservable$2;

exports.toPromise = toPromise$1;
