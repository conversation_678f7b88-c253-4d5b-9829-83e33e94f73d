{"version": 3, "file": "Orientation.js", "names": ["_iosPlugins", "data", "require", "withOrientation", "createInfoPlistPluginWithPropertyGuard", "setOrientation", "infoPlistProperty", "expoConfigProperty", "exports", "getOrientation", "config", "_config$orientation", "orientation", "PORTRAIT_ORIENTATIONS", "LANDSCAPE_ORIENTATIONS", "getUISupportedInterfaceOrientations", "infoPlist", "UISupportedInterfaceOrientations"], "sources": ["../../src/ios/Orientation.ts"], "sourcesContent": ["import { ExpoConfig } from '@expo/config-types';\n\nimport { createInfoPlistPluginWithPropertyGuard } from '../plugins/ios-plugins';\nimport { InfoPlist, InterfaceOrientation } from './IosConfig.types';\n\nexport const withOrientation = createInfoPlistPluginWithPropertyGuard(\n  setOrientation,\n  {\n    infoPlistProperty: 'UISupportedInterfaceOrientations',\n    expoConfigProperty: 'orientation',\n  },\n  'withOrientation'\n);\n\nexport function getOrientation(config: Pick<ExpoConfig, 'orientation'>) {\n  return config.orientation ?? null;\n}\n\nexport const PORTRAIT_ORIENTATIONS: InterfaceOrientation[] = [\n  'UIInterfaceOrientationPortrait',\n  'UIInterfaceOrientationPortraitUpsideDown',\n];\n\nexport const LANDSCAPE_ORIENTATIONS: InterfaceOrientation[] = [\n  'UIInterfaceOrientationLandscapeLeft',\n  'UIInterfaceOrientationLandscapeRight',\n];\n\nfunction getUISupportedInterfaceOrientations(orientation: string | null): InterfaceOrientation[] {\n  if (orientation === 'portrait') {\n    return PORTRAIT_ORIENTATIONS;\n  } else if (orientation === 'landscape') {\n    return LANDSCAPE_ORIENTATIONS;\n  } else {\n    return [...PORTRAIT_ORIENTATIONS, ...LANDSCAPE_ORIENTATIONS];\n  }\n}\n\nexport function setOrientation(\n  config: Pick<ExpoConfig, 'orientation'>,\n  infoPlist: InfoPlist\n): InfoPlist {\n  const orientation = getOrientation(config);\n\n  return {\n    ...infoPlist,\n    UISupportedInterfaceOrientations: getUISupportedInterfaceOrientations(orientation),\n  };\n}\n"], "mappings": ";;;;;;;;;AAEA,SAAAA,YAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,WAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAGO,MAAME,eAAe,GAAG,IAAAC,oDAAsC,EACnEC,cAAc,EACd;EACEC,iBAAiB,EAAE,kCAAkC;EACrDC,kBAAkB,EAAE;AACtB,CAAC,EACD,iBAAiB,CAClB;AAACC,OAAA,CAAAL,eAAA,GAAAA,eAAA;AAEK,SAASM,cAAcA,CAACC,MAAuC,EAAE;EAAA,IAAAC,mBAAA;EACtE,QAAAA,mBAAA,GAAOD,MAAM,CAACE,WAAW,cAAAD,mBAAA,cAAAA,mBAAA,GAAI,IAAI;AACnC;AAEO,MAAME,qBAA6C,GAAG,CAC3D,gCAAgC,EAChC,0CAA0C,CAC3C;AAACL,OAAA,CAAAK,qBAAA,GAAAA,qBAAA;AAEK,MAAMC,sBAA8C,GAAG,CAC5D,qCAAqC,EACrC,sCAAsC,CACvC;AAACN,OAAA,CAAAM,sBAAA,GAAAA,sBAAA;AAEF,SAASC,mCAAmCA,CAACH,WAA0B,EAA0B;EAC/F,IAAIA,WAAW,KAAK,UAAU,EAAE;IAC9B,OAAOC,qBAAqB;EAC9B,CAAC,MAAM,IAAID,WAAW,KAAK,WAAW,EAAE;IACtC,OAAOE,sBAAsB;EAC/B,CAAC,MAAM;IACL,OAAO,CAAC,GAAGD,qBAAqB,EAAE,GAAGC,sBAAsB,CAAC;EAC9D;AACF;AAEO,SAAST,cAAcA,CAC5BK,MAAuC,EACvCM,SAAoB,EACT;EACX,MAAMJ,WAAW,GAAGH,cAAc,CAACC,MAAM,CAAC;EAE1C,OAAO;IACL,GAAGM,SAAS;IACZC,gCAAgC,EAAEF,mCAAmC,CAACH,WAAW;EACnF,CAAC;AACH"}