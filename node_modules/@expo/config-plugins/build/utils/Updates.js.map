{"version": 3, "file": "Updates.js", "names": ["_sdkRuntimeVersions", "data", "require", "_fs", "_interopRequireDefault", "_getenv", "_path", "_resolveFrom", "_semver", "_", "obj", "__esModule", "default", "getExpoUpdatesPackageVersion", "projectRoot", "expoUpdatesPackageJsonPath", "resolveFrom", "silent", "fs", "existsSync", "packageJson", "JSON", "parse", "readFileSync", "version", "shouldDefaultToClassicUpdates", "config", "_config$updates", "updates", "useClassicUpdates", "getUpdateUrl", "username", "_config$updates2", "url", "_config$updates3", "user", "owner", "slug", "getAppVersion", "_config$version", "getNativeVersion", "platform", "IOSConfig", "Version", "getVersion", "buildNumber", "getBuildNumber", "versionCode", "AndroidConfig", "getVersionCode", "Error", "withRuntimeVersion", "_config$ios", "_config$android", "ios", "runtimeVersion", "getRuntimeVersion", "android", "exports", "getRuntimeVersionNullable", "e", "boolish", "console", "log", "_config$platform$runt", "_config$platform", "policy", "sdkVersion", "getRuntimeVersionForSDKVersion", "stringify", "getSDKVersion", "getUpdatesEnabled", "_config$updates4", "enabled", "undefined", "getUpdatesTimeout", "_config$updates$fallb", "_config$updates5", "fallbackToCacheTimeout", "getUpdatesCheckOnLaunch", "expoUpdatesPackageVersion", "_config$updates6", "_config$updates7", "_config$updates8", "_config$updates9", "checkAutomatically", "semver", "gte", "getUpdatesCodeSigningCertificate", "_config$updates10", "codeSigningCertificatePath", "codeSigningCertificate", "finalPath", "path", "join", "getUpdatesCodeSigningMetadata", "_config$updates11", "codeSigningMetadata", "getUpdatesCodeSigningMetadataStringified", "metadata", "getUpdatesRequestHeaders", "_config$updates12", "requestHeaders", "getUpdatesRequestHeadersStringified"], "sources": ["../../src/utils/Updates.ts"], "sourcesContent": ["import { Android, ExpoConfig, IOS } from '@expo/config-types';\nimport { getRuntimeVersionForSDKVersion } from '@expo/sdk-runtime-versions';\nimport fs from 'fs';\nimport { boolish } from 'getenv';\nimport path from 'path';\nimport resolveFrom from 'resolve-from';\nimport semver from 'semver';\n\nimport { AndroidConfig, IOSConfig } from '..';\n\nexport type ExpoConfigUpdates = Pick<\n  ExpoConfig,\n  'sdkVersion' | 'owner' | 'runtimeVersion' | 'updates' | 'slug'\n>;\n\nexport function getExpoUpdatesPackageVersion(projectRoot: string): string | null {\n  const expoUpdatesPackageJsonPath = resolveFrom.silent(projectRoot, 'expo-updates/package.json');\n  if (!expoUpdatesPackageJsonPath || !fs.existsSync(expoUpdatesPackageJsonPath)) {\n    return null;\n  }\n  const packageJson = JSON.parse(fs.readFileSync(expoUpdatesPackageJsonPath, 'utf8'));\n  return packageJson.version;\n}\n\nfunction shouldDefaultToClassicUpdates(config: Pick<ExpoConfigUpdates, 'updates'>): boolean {\n  return !!config.updates?.useClassicUpdates;\n}\n\nexport function getUpdateUrl(\n  config: Pick<ExpoConfigUpdates, 'owner' | 'slug' | 'updates'>,\n  username: string | null\n): string | null {\n  if (config.updates?.url) {\n    return config.updates?.url;\n  }\n\n  if (!shouldDefaultToClassicUpdates(config)) {\n    return null;\n  }\n\n  const user = typeof config.owner === 'string' ? config.owner : username;\n  if (!user) {\n    return null;\n  }\n  return `https://exp.host/@${user}/${config.slug}`;\n}\n\nexport function getAppVersion(config: Pick<ExpoConfig, 'version'>): string {\n  return config.version ?? '1.0.0';\n}\n\nexport function getNativeVersion(\n  config: Pick<ExpoConfig, 'version'> & {\n    android?: Pick<Android, 'versionCode'>;\n    ios?: Pick<IOS, 'buildNumber'>;\n  },\n  platform: 'android' | 'ios'\n): string {\n  const version = IOSConfig.Version.getVersion(config);\n  switch (platform) {\n    case 'ios': {\n      const buildNumber = IOSConfig.Version.getBuildNumber(config);\n      return `${version}(${buildNumber})`;\n    }\n    case 'android': {\n      const versionCode = AndroidConfig.Version.getVersionCode(config);\n      return `${version}(${versionCode})`;\n    }\n    default: {\n      throw new Error(\n        `\"${platform}\" is not a supported platform. Choose either \"ios\" or \"android\".`\n      );\n    }\n  }\n}\n\n/**\n * Compute runtime version policies.\n * @return an expoConfig with only string valued platform specific runtime versions.\n */\nexport const withRuntimeVersion: (config: ExpoConfig) => ExpoConfig = (config) => {\n  if (config.ios?.runtimeVersion || config.runtimeVersion) {\n    const runtimeVersion = getRuntimeVersion(config, 'ios');\n    if (runtimeVersion) {\n      config.ios = {\n        ...config.ios,\n        runtimeVersion,\n      };\n    }\n  }\n  if (config.android?.runtimeVersion || config.runtimeVersion) {\n    const runtimeVersion = getRuntimeVersion(config, 'android');\n    if (runtimeVersion) {\n      config.android = {\n        ...config.android,\n        runtimeVersion,\n      };\n    }\n  }\n  delete config.runtimeVersion;\n  return config;\n};\n\nexport function getRuntimeVersionNullable(\n  ...[config, platform]: Parameters<typeof getRuntimeVersion>\n): string | null {\n  try {\n    return getRuntimeVersion(config, platform);\n  } catch (e) {\n    if (boolish('EXPO_DEBUG', false)) {\n      console.log(e);\n    }\n    return null;\n  }\n}\n\nexport function getRuntimeVersion(\n  config: Pick<ExpoConfig, 'version' | 'runtimeVersion' | 'sdkVersion'> & {\n    android?: Pick<Android, 'versionCode' | 'runtimeVersion'>;\n    ios?: Pick<IOS, 'buildNumber' | 'runtimeVersion'>;\n  },\n  platform: 'android' | 'ios'\n): string | null {\n  const runtimeVersion = config[platform]?.runtimeVersion ?? config.runtimeVersion;\n  if (!runtimeVersion) {\n    return null;\n  }\n\n  if (typeof runtimeVersion === 'string') {\n    return runtimeVersion;\n  } else if (runtimeVersion.policy === 'appVersion') {\n    return getAppVersion(config);\n  } else if (runtimeVersion.policy === 'nativeVersion') {\n    return getNativeVersion(config, platform);\n  } else if (runtimeVersion.policy === 'sdkVersion') {\n    if (!config.sdkVersion) {\n      throw new Error(\"An SDK version must be defined when using the 'sdkVersion' runtime policy.\");\n    }\n    return getRuntimeVersionForSDKVersion(config.sdkVersion);\n  }\n\n  throw new Error(\n    `\"${\n      typeof runtimeVersion === 'object' ? JSON.stringify(runtimeVersion) : runtimeVersion\n    }\" is not a valid runtime version. getRuntimeVersion only supports a string, \"sdkVersion\", \"appVersion\", or \"nativeVersion\" policy.`\n  );\n}\n\nexport function getSDKVersion(config: Pick<ExpoConfigUpdates, 'sdkVersion'>): string | null {\n  return typeof config.sdkVersion === 'string' ? config.sdkVersion : null;\n}\n\nexport function getUpdatesEnabled(\n  config: Pick<ExpoConfigUpdates, 'owner' | 'slug' | 'updates'>,\n  username: string | null\n): boolean {\n  // allow override of enabled property\n  if (config.updates?.enabled !== undefined) {\n    return config.updates.enabled;\n  }\n\n  // enable if URL is set (which respects shouldDefaultToClassicUpdates)\n  return getUpdateUrl(config, username) !== null;\n}\n\nexport function getUpdatesTimeout(config: Pick<ExpoConfigUpdates, 'updates'>): number {\n  return config.updates?.fallbackToCacheTimeout ?? 0;\n}\n\nexport function getUpdatesCheckOnLaunch(\n  config: Pick<ExpoConfigUpdates, 'updates'>,\n  expoUpdatesPackageVersion?: string | null\n): 'NEVER' | 'ERROR_RECOVERY_ONLY' | 'ALWAYS' | 'WIFI_ONLY' {\n  if (config.updates?.checkAutomatically === 'ON_ERROR_RECOVERY') {\n    // native 'ERROR_RECOVERY_ONLY' option was only introduced in 0.11.x\n    if (expoUpdatesPackageVersion && semver.gte(expoUpdatesPackageVersion, '0.11.0')) {\n      return 'ERROR_RECOVERY_ONLY';\n    }\n    return 'NEVER';\n  } else if (config.updates?.checkAutomatically === 'ON_LOAD') {\n    return 'ALWAYS';\n  } else if (config.updates?.checkAutomatically === 'WIFI_ONLY') {\n    return 'WIFI_ONLY';\n  } else if (config.updates?.checkAutomatically === 'NEVER') {\n    return 'NEVER';\n  }\n  return 'ALWAYS';\n}\n\nexport function getUpdatesCodeSigningCertificate(\n  projectRoot: string,\n  config: Pick<ExpoConfigUpdates, 'updates'>\n): string | undefined {\n  const codeSigningCertificatePath = config.updates?.codeSigningCertificate;\n  if (!codeSigningCertificatePath) {\n    return undefined;\n  }\n\n  const finalPath = path.join(projectRoot, codeSigningCertificatePath);\n  if (!fs.existsSync(finalPath)) {\n    throw new Error(`File not found at \\`updates.codeSigningCertificate\\` path: ${finalPath}`);\n  }\n\n  return fs.readFileSync(finalPath, 'utf8');\n}\n\nexport function getUpdatesCodeSigningMetadata(\n  config: Pick<ExpoConfigUpdates, 'updates'>\n): NonNullable<ExpoConfigUpdates['updates']>['codeSigningMetadata'] {\n  return config.updates?.codeSigningMetadata;\n}\n\nexport function getUpdatesCodeSigningMetadataStringified(\n  config: Pick<ExpoConfigUpdates, 'updates'>\n): string | undefined {\n  const metadata = getUpdatesCodeSigningMetadata(config);\n  if (!metadata) {\n    return undefined;\n  }\n\n  return JSON.stringify(metadata);\n}\n\nexport function getUpdatesRequestHeaders(\n  config: Pick<ExpoConfigUpdates, 'updates'>\n): NonNullable<ExpoConfigUpdates['updates']>['requestHeaders'] {\n  return config.updates?.requestHeaders;\n}\n\nexport function getUpdatesRequestHeadersStringified(\n  config: Pick<ExpoConfigUpdates, 'updates'>\n): string | undefined {\n  const metadata = getUpdatesRequestHeaders(config);\n  if (!metadata) {\n    return undefined;\n  }\n\n  return JSON.stringify(metadata);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AACA,SAAAA,oBAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,mBAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAE,IAAA;EAAA,MAAAF,IAAA,GAAAG,sBAAA,CAAAF,OAAA;EAAAC,GAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAI,QAAA;EAAA,MAAAJ,IAAA,GAAAC,OAAA;EAAAG,OAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAK,MAAA;EAAA,MAAAL,IAAA,GAAAG,sBAAA,CAAAF,OAAA;EAAAI,KAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAM,aAAA;EAAA,MAAAN,IAAA,GAAAG,sBAAA,CAAAF,OAAA;EAAAK,YAAA,YAAAA,CAAA;IAAA,OAAAN,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAO,QAAA;EAAA,MAAAP,IAAA,GAAAG,sBAAA,CAAAF,OAAA;EAAAM,OAAA,YAAAA,CAAA;IAAA,OAAAP,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAQ,EAAA;EAAA,MAAAR,IAAA,GAAAC,OAAA;EAAAO,CAAA,YAAAA,CAAA;IAAA,OAAAR,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA8C,SAAAG,uBAAAM,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAOvC,SAASG,4BAA4BA,CAACC,WAAmB,EAAiB;EAC/E,MAAMC,0BAA0B,GAAGC,sBAAW,CAACC,MAAM,CAACH,WAAW,EAAE,2BAA2B,CAAC;EAC/F,IAAI,CAACC,0BAA0B,IAAI,CAACG,aAAE,CAACC,UAAU,CAACJ,0BAA0B,CAAC,EAAE;IAC7E,OAAO,IAAI;EACb;EACA,MAAMK,WAAW,GAAGC,IAAI,CAACC,KAAK,CAACJ,aAAE,CAACK,YAAY,CAACR,0BAA0B,EAAE,MAAM,CAAC,CAAC;EACnF,OAAOK,WAAW,CAACI,OAAO;AAC5B;AAEA,SAASC,6BAA6BA,CAACC,MAA0C,EAAW;EAAA,IAAAC,eAAA;EAC1F,OAAO,CAAC,GAAAA,eAAA,GAACD,MAAM,CAACE,OAAO,cAAAD,eAAA,eAAdA,eAAA,CAAgBE,iBAAiB;AAC5C;AAEO,SAASC,YAAYA,CAC1BJ,MAA6D,EAC7DK,QAAuB,EACR;EAAA,IAAAC,gBAAA;EACf,KAAAA,gBAAA,GAAIN,MAAM,CAACE,OAAO,cAAAI,gBAAA,eAAdA,gBAAA,CAAgBC,GAAG,EAAE;IAAA,IAAAC,gBAAA;IACvB,QAAAA,gBAAA,GAAOR,MAAM,CAACE,OAAO,cAAAM,gBAAA,uBAAdA,gBAAA,CAAgBD,GAAG;EAC5B;EAEA,IAAI,CAACR,6BAA6B,CAACC,MAAM,CAAC,EAAE;IAC1C,OAAO,IAAI;EACb;EAEA,MAAMS,IAAI,GAAG,OAAOT,MAAM,CAACU,KAAK,KAAK,QAAQ,GAAGV,MAAM,CAACU,KAAK,GAAGL,QAAQ;EACvE,IAAI,CAACI,IAAI,EAAE;IACT,OAAO,IAAI;EACb;EACA,OAAQ,qBAAoBA,IAAK,IAAGT,MAAM,CAACW,IAAK,EAAC;AACnD;AAEO,SAASC,aAAaA,CAACZ,MAAmC,EAAU;EAAA,IAAAa,eAAA;EACzE,QAAAA,eAAA,GAAOb,MAAM,CAACF,OAAO,cAAAe,eAAA,cAAAA,eAAA,GAAI,OAAO;AAClC;AAEO,SAASC,gBAAgBA,CAC9Bd,MAGC,EACDe,QAA2B,EACnB;EACR,MAAMjB,OAAO,GAAGkB,aAAS,CAACC,OAAO,CAACC,UAAU,CAAClB,MAAM,CAAC;EACpD,QAAQe,QAAQ;IACd,KAAK,KAAK;MAAE;QACV,MAAMI,WAAW,GAAGH,aAAS,CAACC,OAAO,CAACG,cAAc,CAACpB,MAAM,CAAC;QAC5D,OAAQ,GAAEF,OAAQ,IAAGqB,WAAY,GAAE;MACrC;IACA,KAAK,SAAS;MAAE;QACd,MAAME,WAAW,GAAGC,iBAAa,CAACL,OAAO,CAACM,cAAc,CAACvB,MAAM,CAAC;QAChE,OAAQ,GAAEF,OAAQ,IAAGuB,WAAY,GAAE;MACrC;IACA;MAAS;QACP,MAAM,IAAIG,KAAK,CACZ,IAAGT,QAAS,kEAAiE,CAC/E;MACH;EAAC;AAEL;;AAEA;AACA;AACA;AACA;AACO,MAAMU,kBAAsD,GAAIzB,MAAM,IAAK;EAAA,IAAA0B,WAAA,EAAAC,eAAA;EAChF,IAAI,CAAAD,WAAA,GAAA1B,MAAM,CAAC4B,GAAG,cAAAF,WAAA,eAAVA,WAAA,CAAYG,cAAc,IAAI7B,MAAM,CAAC6B,cAAc,EAAE;IACvD,MAAMA,cAAc,GAAGC,iBAAiB,CAAC9B,MAAM,EAAE,KAAK,CAAC;IACvD,IAAI6B,cAAc,EAAE;MAClB7B,MAAM,CAAC4B,GAAG,GAAG;QACX,GAAG5B,MAAM,CAAC4B,GAAG;QACbC;MACF,CAAC;IACH;EACF;EACA,IAAI,CAAAF,eAAA,GAAA3B,MAAM,CAAC+B,OAAO,cAAAJ,eAAA,eAAdA,eAAA,CAAgBE,cAAc,IAAI7B,MAAM,CAAC6B,cAAc,EAAE;IAC3D,MAAMA,cAAc,GAAGC,iBAAiB,CAAC9B,MAAM,EAAE,SAAS,CAAC;IAC3D,IAAI6B,cAAc,EAAE;MAClB7B,MAAM,CAAC+B,OAAO,GAAG;QACf,GAAG/B,MAAM,CAAC+B,OAAO;QACjBF;MACF,CAAC;IACH;EACF;EACA,OAAO7B,MAAM,CAAC6B,cAAc;EAC5B,OAAO7B,MAAM;AACf,CAAC;AAACgC,OAAA,CAAAP,kBAAA,GAAAA,kBAAA;AAEK,SAASQ,yBAAyBA,CACvC,GAAG,CAACjC,MAAM,EAAEe,QAAQ,CAAuC,EAC5C;EACf,IAAI;IACF,OAAOe,iBAAiB,CAAC9B,MAAM,EAAEe,QAAQ,CAAC;EAC5C,CAAC,CAAC,OAAOmB,CAAC,EAAE;IACV,IAAI,IAAAC,iBAAO,EAAC,YAAY,EAAE,KAAK,CAAC,EAAE;MAChCC,OAAO,CAACC,GAAG,CAACH,CAAC,CAAC;IAChB;IACA,OAAO,IAAI;EACb;AACF;AAEO,SAASJ,iBAAiBA,CAC/B9B,MAGC,EACDe,QAA2B,EACZ;EAAA,IAAAuB,qBAAA,EAAAC,gBAAA;EACf,MAAMV,cAAc,IAAAS,qBAAA,IAAAC,gBAAA,GAAGvC,MAAM,CAACe,QAAQ,CAAC,cAAAwB,gBAAA,uBAAhBA,gBAAA,CAAkBV,cAAc,cAAAS,qBAAA,cAAAA,qBAAA,GAAItC,MAAM,CAAC6B,cAAc;EAChF,IAAI,CAACA,cAAc,EAAE;IACnB,OAAO,IAAI;EACb;EAEA,IAAI,OAAOA,cAAc,KAAK,QAAQ,EAAE;IACtC,OAAOA,cAAc;EACvB,CAAC,MAAM,IAAIA,cAAc,CAACW,MAAM,KAAK,YAAY,EAAE;IACjD,OAAO5B,aAAa,CAACZ,MAAM,CAAC;EAC9B,CAAC,MAAM,IAAI6B,cAAc,CAACW,MAAM,KAAK,eAAe,EAAE;IACpD,OAAO1B,gBAAgB,CAACd,MAAM,EAAEe,QAAQ,CAAC;EAC3C,CAAC,MAAM,IAAIc,cAAc,CAACW,MAAM,KAAK,YAAY,EAAE;IACjD,IAAI,CAACxC,MAAM,CAACyC,UAAU,EAAE;MACtB,MAAM,IAAIjB,KAAK,CAAC,4EAA4E,CAAC;IAC/F;IACA,OAAO,IAAAkB,oDAA8B,EAAC1C,MAAM,CAACyC,UAAU,CAAC;EAC1D;EAEA,MAAM,IAAIjB,KAAK,CACZ,IACC,OAAOK,cAAc,KAAK,QAAQ,GAAGlC,IAAI,CAACgD,SAAS,CAACd,cAAc,CAAC,GAAGA,cACvE,oIAAmI,CACrI;AACH;AAEO,SAASe,aAAaA,CAAC5C,MAA6C,EAAiB;EAC1F,OAAO,OAAOA,MAAM,CAACyC,UAAU,KAAK,QAAQ,GAAGzC,MAAM,CAACyC,UAAU,GAAG,IAAI;AACzE;AAEO,SAASI,iBAAiBA,CAC/B7C,MAA6D,EAC7DK,QAAuB,EACd;EAAA,IAAAyC,gBAAA;EACT;EACA,IAAI,EAAAA,gBAAA,GAAA9C,MAAM,CAACE,OAAO,cAAA4C,gBAAA,uBAAdA,gBAAA,CAAgBC,OAAO,MAAKC,SAAS,EAAE;IACzC,OAAOhD,MAAM,CAACE,OAAO,CAAC6C,OAAO;EAC/B;;EAEA;EACA,OAAO3C,YAAY,CAACJ,MAAM,EAAEK,QAAQ,CAAC,KAAK,IAAI;AAChD;AAEO,SAAS4C,iBAAiBA,CAACjD,MAA0C,EAAU;EAAA,IAAAkD,qBAAA,EAAAC,gBAAA;EACpF,QAAAD,qBAAA,IAAAC,gBAAA,GAAOnD,MAAM,CAACE,OAAO,cAAAiD,gBAAA,uBAAdA,gBAAA,CAAgBC,sBAAsB,cAAAF,qBAAA,cAAAA,qBAAA,GAAI,CAAC;AACpD;AAEO,SAASG,uBAAuBA,CACrCrD,MAA0C,EAC1CsD,yBAAyC,EACiB;EAAA,IAAAC,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA;EAC1D,IAAI,EAAAH,gBAAA,GAAAvD,MAAM,CAACE,OAAO,cAAAqD,gBAAA,uBAAdA,gBAAA,CAAgBI,kBAAkB,MAAK,mBAAmB,EAAE;IAC9D;IACA,IAAIL,yBAAyB,IAAIM,iBAAM,CAACC,GAAG,CAACP,yBAAyB,EAAE,QAAQ,CAAC,EAAE;MAChF,OAAO,qBAAqB;IAC9B;IACA,OAAO,OAAO;EAChB,CAAC,MAAM,IAAI,EAAAE,gBAAA,GAAAxD,MAAM,CAACE,OAAO,cAAAsD,gBAAA,uBAAdA,gBAAA,CAAgBG,kBAAkB,MAAK,SAAS,EAAE;IAC3D,OAAO,QAAQ;EACjB,CAAC,MAAM,IAAI,EAAAF,gBAAA,GAAAzD,MAAM,CAACE,OAAO,cAAAuD,gBAAA,uBAAdA,gBAAA,CAAgBE,kBAAkB,MAAK,WAAW,EAAE;IAC7D,OAAO,WAAW;EACpB,CAAC,MAAM,IAAI,EAAAD,gBAAA,GAAA1D,MAAM,CAACE,OAAO,cAAAwD,gBAAA,uBAAdA,gBAAA,CAAgBC,kBAAkB,MAAK,OAAO,EAAE;IACzD,OAAO,OAAO;EAChB;EACA,OAAO,QAAQ;AACjB;AAEO,SAASG,gCAAgCA,CAC9C1E,WAAmB,EACnBY,MAA0C,EACtB;EAAA,IAAA+D,iBAAA;EACpB,MAAMC,0BAA0B,IAAAD,iBAAA,GAAG/D,MAAM,CAACE,OAAO,cAAA6D,iBAAA,uBAAdA,iBAAA,CAAgBE,sBAAsB;EACzE,IAAI,CAACD,0BAA0B,EAAE;IAC/B,OAAOhB,SAAS;EAClB;EAEA,MAAMkB,SAAS,GAAGC,eAAI,CAACC,IAAI,CAAChF,WAAW,EAAE4E,0BAA0B,CAAC;EACpE,IAAI,CAACxE,aAAE,CAACC,UAAU,CAACyE,SAAS,CAAC,EAAE;IAC7B,MAAM,IAAI1C,KAAK,CAAE,8DAA6D0C,SAAU,EAAC,CAAC;EAC5F;EAEA,OAAO1E,aAAE,CAACK,YAAY,CAACqE,SAAS,EAAE,MAAM,CAAC;AAC3C;AAEO,SAASG,6BAA6BA,CAC3CrE,MAA0C,EACwB;EAAA,IAAAsE,iBAAA;EAClE,QAAAA,iBAAA,GAAOtE,MAAM,CAACE,OAAO,cAAAoE,iBAAA,uBAAdA,iBAAA,CAAgBC,mBAAmB;AAC5C;AAEO,SAASC,wCAAwCA,CACtDxE,MAA0C,EACtB;EACpB,MAAMyE,QAAQ,GAAGJ,6BAA6B,CAACrE,MAAM,CAAC;EACtD,IAAI,CAACyE,QAAQ,EAAE;IACb,OAAOzB,SAAS;EAClB;EAEA,OAAOrD,IAAI,CAACgD,SAAS,CAAC8B,QAAQ,CAAC;AACjC;AAEO,SAASC,wBAAwBA,CACtC1E,MAA0C,EACmB;EAAA,IAAA2E,iBAAA;EAC7D,QAAAA,iBAAA,GAAO3E,MAAM,CAACE,OAAO,cAAAyE,iBAAA,uBAAdA,iBAAA,CAAgBC,cAAc;AACvC;AAEO,SAASC,mCAAmCA,CACjD7E,MAA0C,EACtB;EACpB,MAAMyE,QAAQ,GAAGC,wBAAwB,CAAC1E,MAAM,CAAC;EACjD,IAAI,CAACyE,QAAQ,EAAE;IACb,OAAOzB,SAAS;EAClB;EAEA,OAAOrD,IAAI,CAACgD,SAAS,CAAC8B,QAAQ,CAAC;AACjC"}