{"version": 3, "file": "Package.js", "names": ["_debug", "data", "_interopRequireDefault", "require", "_fs", "_glob", "_path", "_androidPlugins", "_withDangerousMod", "_modules", "_warnings", "_Paths", "obj", "__esModule", "default", "debug", "Debug", "withPackageGradle", "config", "withAppBuildGradle", "modResults", "language", "contents", "setPackageInBuildGradle", "addWarningAndroid", "exports", "withPackageRefactor", "withDangerousMod", "renamePackageOnDisk", "modRequest", "projectRoot", "getPackage", "_config$android$packa", "_config$android", "android", "package", "getPackageRoot", "type", "path", "join", "getCurrentPackageName", "packageRoot", "mainApplication", "getProjectFilePath", "packagePath", "dirname", "packagePathParts", "relative", "split", "sep", "filter", "Boolean", "getCurrentPackageForProjectFile", "fileName", "filePath", "globSync", "getCurrentPackageNameForType", "newPackageName", "renameJniOnDiskForType", "packageName", "renamePackageOnDiskForType", "currentPackageName", "jniRoot", "filesToUpdate", "cwd", "absolute", "for<PERSON>ach", "filepath", "fs", "lstatSync", "isFile", "includes", "extname", "readFileSync", "toString", "replace", "RegExp", "transformJavaClassDescriptor", "writeFileSync", "directoryExistsAsync", "currentPackagePath", "newPackagePath", "mkdirSync", "recursive", "relativePath", "moveFileSync", "oldPathParts", "length", "pathToCheck", "files", "readdirSync", "rmdirSync", "pop", "push", "src", "dest", "renameSync", "buildGradle", "pattern", "getApplicationIdAsync", "_matchResult$", "buildGradlePath", "getAppBuildGradleFilePath", "existsSync", "promises", "readFile", "matchResult", "match"], "sources": ["../../src/android/Package.ts"], "sourcesContent": ["import { ExpoConfig } from '@expo/config-types';\nimport Debug from 'debug';\nimport fs from 'fs';\nimport { sync as globSync } from 'glob';\nimport path from 'path';\n\nimport { ConfigPlugin } from '../Plugin.types';\nimport { withAppBuildGradle } from '../plugins/android-plugins';\nimport { withDangerousMod } from '../plugins/withDangerousMod';\nimport { directoryExistsAsync } from '../utils/modules';\nimport { addWarningAndroid } from '../utils/warnings';\nimport { getAppBuildGradleFilePath, getProjectFilePath } from './Paths';\n\nconst debug = Debug('expo:config-plugins:android:package');\n\nexport const withPackageGradle: ConfigPlugin = (config) => {\n  return withAppBuildGradle(config, (config) => {\n    if (config.modResults.language === 'groovy') {\n      config.modResults.contents = setPackageInBuildGradle(config, config.modResults.contents);\n    } else {\n      addWarningAndroid(\n        'android.package',\n        `Cannot automatically configure app build.gradle if it's not groovy`\n      );\n    }\n    return config;\n  });\n};\n\nexport const withPackageRefactor: ConfigPlugin = (config) => {\n  return withDangerousMod(config, [\n    'android',\n    async (config) => {\n      await renamePackageOnDisk(config, config.modRequest.projectRoot);\n      return config;\n    },\n  ]);\n};\n\nexport function getPackage(config: Pick<ExpoConfig, 'android'>) {\n  return config.android?.package ?? null;\n}\n\nfunction getPackageRoot(projectRoot: string, type: 'main' | 'debug') {\n  return path.join(projectRoot, 'android', 'app', 'src', type, 'java');\n}\n\nfunction getCurrentPackageName(projectRoot: string, packageRoot: string) {\n  const mainApplication = getProjectFilePath(projectRoot, 'MainApplication');\n  const packagePath = path.dirname(mainApplication);\n  const packagePathParts = path.relative(packageRoot, packagePath).split(path.sep).filter(Boolean);\n\n  return packagePathParts.join('.');\n}\n\nfunction getCurrentPackageForProjectFile(\n  projectRoot: string,\n  packageRoot: string,\n  fileName: string,\n  type: string\n) {\n  const filePath = globSync(\n    path.join(projectRoot, `android/app/src/${type}/java/**/${fileName}.@(java|kt)`)\n  )[0];\n\n  if (!filePath) {\n    return null;\n  }\n\n  const packagePath = path.dirname(filePath);\n  const packagePathParts = path.relative(packageRoot, packagePath).split(path.sep).filter(Boolean);\n\n  return packagePathParts.join('.');\n}\n\nfunction getCurrentPackageNameForType(projectRoot: string, type: string): string | null {\n  const packageRoot = getPackageRoot(projectRoot, type as any);\n\n  if (type === 'main') {\n    return getCurrentPackageName(projectRoot, packageRoot);\n  }\n  // debug, etc..\n  return getCurrentPackageForProjectFile(projectRoot, packageRoot, '*', type);\n}\n\n// NOTE(brentvatne): this assumes that our MainApplication.java file is in the root of the package\n// this makes sense for standard react-native projects but may not apply in customized projects, so if\n// we want this to be runnable in any app we need to handle other possibilities\nexport async function renamePackageOnDisk(\n  config: Pick<ExpoConfig, 'android'>,\n  projectRoot: string\n) {\n  const newPackageName = getPackage(config);\n  if (newPackageName === null) {\n    return;\n  }\n\n  for (const type of ['debug', 'main', 'release']) {\n    await renameJniOnDiskForType({ projectRoot, type, packageName: newPackageName });\n    await renamePackageOnDiskForType({ projectRoot, type, packageName: newPackageName });\n  }\n}\n\nexport async function renameJniOnDiskForType({\n  projectRoot,\n  type,\n  packageName,\n}: {\n  projectRoot: string;\n  type: string;\n  packageName: string;\n}) {\n  if (!packageName) {\n    return;\n  }\n\n  const currentPackageName = getCurrentPackageNameForType(projectRoot, type);\n  if (!currentPackageName || !packageName || currentPackageName === packageName) {\n    return;\n  }\n\n  const jniRoot = path.join(projectRoot, 'android', 'app', 'src', type, 'jni');\n  const filesToUpdate = [...globSync('**/*', { cwd: jniRoot, absolute: true })];\n  // Replace all occurrences of the path in the project\n  filesToUpdate.forEach((filepath: string) => {\n    try {\n      if (fs.lstatSync(filepath).isFile() && ['.h', '.cpp'].includes(path.extname(filepath))) {\n        let contents = fs.readFileSync(filepath).toString();\n        contents = contents.replace(\n          new RegExp(transformJavaClassDescriptor(currentPackageName).replace(/\\//g, '\\\\/'), 'g'),\n          transformJavaClassDescriptor(packageName)\n        );\n        fs.writeFileSync(filepath, contents);\n      }\n    } catch {\n      debug(`Error updating \"${filepath}\" for type \"${type}\"`);\n    }\n  });\n}\n\nexport async function renamePackageOnDiskForType({\n  projectRoot,\n  type,\n  packageName,\n}: {\n  projectRoot: string;\n  type: string;\n  packageName: string;\n}) {\n  if (!packageName) {\n    return;\n  }\n\n  const currentPackageName = getCurrentPackageNameForType(projectRoot, type);\n  debug(`Found package \"${currentPackageName}\" for type \"${type}\"`);\n  if (!currentPackageName || currentPackageName === packageName) {\n    return;\n  }\n  debug(`Refactor \"${currentPackageName}\" to \"${packageName}\" for type \"${type}\"`);\n  const packageRoot = getPackageRoot(projectRoot, type as any);\n  // Set up our paths\n  if (!(await directoryExistsAsync(packageRoot))) {\n    debug(`- skipping refactor of missing directory: ${packageRoot}`);\n    return;\n  }\n\n  const currentPackagePath = path.join(packageRoot, ...currentPackageName.split('.'));\n  const newPackagePath = path.join(packageRoot, ...packageName.split('.'));\n\n  // Create the new directory\n  fs.mkdirSync(newPackagePath, { recursive: true });\n\n  // Move everything from the old directory over\n  globSync('**/*', { cwd: currentPackagePath }).forEach((relativePath) => {\n    const filepath = path.join(currentPackagePath, relativePath);\n    if (fs.lstatSync(filepath).isFile()) {\n      moveFileSync(filepath, path.join(newPackagePath, relativePath));\n    } else {\n      fs.mkdirSync(filepath, { recursive: true });\n    }\n  });\n\n  // Remove the old directory recursively from com/old/package to com/old and com,\n  // as long as the directories are empty\n  const oldPathParts = currentPackageName.split('.');\n  while (oldPathParts.length) {\n    const pathToCheck = path.join(packageRoot, ...oldPathParts);\n    try {\n      const files = fs.readdirSync(pathToCheck);\n      if (files.length === 0) {\n        fs.rmdirSync(pathToCheck);\n      }\n    } finally {\n      oldPathParts.pop();\n    }\n  }\n\n  const filesToUpdate = [...globSync('**/*', { cwd: newPackagePath, absolute: true })];\n  // Only update the BUCK file to match the main package name\n  if (type === 'main') {\n    // NOTE(EvanBacon): We dropped this file in SDK 48 but other templates may still use it.\n    filesToUpdate.push(path.join(projectRoot, 'android', 'app', 'BUCK'));\n  }\n  // Replace all occurrences of the path in the project\n  filesToUpdate.forEach((filepath: string) => {\n    try {\n      if (fs.lstatSync(filepath).isFile()) {\n        let contents = fs.readFileSync(filepath).toString();\n        contents = contents.replace(new RegExp(currentPackageName!, 'g'), packageName);\n        if (['.h', '.cpp'].includes(path.extname(filepath))) {\n          contents = contents.replace(\n            new RegExp(transformJavaClassDescriptor(currentPackageName).replace(/\\//g, '\\\\'), 'g'),\n            transformJavaClassDescriptor(packageName)\n          );\n        }\n        fs.writeFileSync(filepath, contents);\n      }\n    } catch {\n      debug(`Error updating \"${filepath}\" for type \"${type}\"`);\n    }\n  });\n}\n\nfunction moveFileSync(src: string, dest: string) {\n  fs.mkdirSync(path.dirname(dest), { recursive: true });\n  fs.renameSync(src, dest);\n}\n\nexport function setPackageInBuildGradle(config: Pick<ExpoConfig, 'android'>, buildGradle: string) {\n  const packageName = getPackage(config);\n  if (packageName === null) {\n    return buildGradle;\n  }\n\n  const pattern = new RegExp(`(applicationId|namespace) ['\"].*['\"]`, 'g');\n  return buildGradle.replace(pattern, `$1 '${packageName}'`);\n}\n\nexport async function getApplicationIdAsync(projectRoot: string): Promise<string | null> {\n  const buildGradlePath = getAppBuildGradleFilePath(projectRoot);\n  if (!fs.existsSync(buildGradlePath)) {\n    return null;\n  }\n  const buildGradle = await fs.promises.readFile(buildGradlePath, 'utf8');\n  const matchResult = buildGradle.match(/applicationId ['\"](.*)['\"]/);\n  // TODO add fallback for legacy cases to read from AndroidManifest.xml\n  return matchResult?.[1] ?? null;\n}\n\n/**\n * Transform a java package name to java class descriptor,\n * e.g. `com.helloworld` -> `Lcom/helloworld`.\n */\nfunction transformJavaClassDescriptor(packageName: string) {\n  return `L${packageName.replace(/\\./g, '/')}`;\n}\n"], "mappings": ";;;;;;;;;;;;AACA,SAAAA,OAAA;EAAA,MAAAC,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAH,MAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,IAAA;EAAA,MAAAH,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAC,GAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAI,MAAA;EAAA,MAAAJ,IAAA,GAAAE,OAAA;EAAAE,KAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAK,MAAA;EAAA,MAAAL,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAG,KAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAGA,SAAAM,gBAAA;EAAA,MAAAN,IAAA,GAAAE,OAAA;EAAAI,eAAA,YAAAA,CAAA;IAAA,OAAAN,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAO,kBAAA;EAAA,MAAAP,IAAA,GAAAE,OAAA;EAAAK,iBAAA,YAAAA,CAAA;IAAA,OAAAP,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAQ,SAAA;EAAA,MAAAR,IAAA,GAAAE,OAAA;EAAAM,QAAA,YAAAA,CAAA;IAAA,OAAAR,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAS,UAAA;EAAA,MAAAT,IAAA,GAAAE,OAAA;EAAAO,SAAA,YAAAA,CAAA;IAAA,OAAAT,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAU,OAAA;EAAA,MAAAV,IAAA,GAAAE,OAAA;EAAAQ,MAAA,YAAAA,CAAA;IAAA,OAAAV,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAwE,SAAAC,uBAAAU,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAExE,MAAMG,KAAK,GAAG,IAAAC,gBAAK,EAAC,qCAAqC,CAAC;AAEnD,MAAMC,iBAA+B,GAAIC,MAAM,IAAK;EACzD,OAAO,IAAAC,oCAAkB,EAACD,MAAM,EAAGA,MAAM,IAAK;IAC5C,IAAIA,MAAM,CAACE,UAAU,CAACC,QAAQ,KAAK,QAAQ,EAAE;MAC3CH,MAAM,CAACE,UAAU,CAACE,QAAQ,GAAGC,uBAAuB,CAACL,MAAM,EAAEA,MAAM,CAACE,UAAU,CAACE,QAAQ,CAAC;IAC1F,CAAC,MAAM;MACL,IAAAE,6BAAiB,EACf,iBAAiB,EAChB,oEAAmE,CACrE;IACH;IACA,OAAON,MAAM;EACf,CAAC,CAAC;AACJ,CAAC;AAACO,OAAA,CAAAR,iBAAA,GAAAA,iBAAA;AAEK,MAAMS,mBAAiC,GAAIR,MAAM,IAAK;EAC3D,OAAO,IAAAS,oCAAgB,EAACT,MAAM,EAAE,CAC9B,SAAS,EACT,MAAOA,MAAM,IAAK;IAChB,MAAMU,mBAAmB,CAACV,MAAM,EAAEA,MAAM,CAACW,UAAU,CAACC,WAAW,CAAC;IAChE,OAAOZ,MAAM;EACf,CAAC,CACF,CAAC;AACJ,CAAC;AAACO,OAAA,CAAAC,mBAAA,GAAAA,mBAAA;AAEK,SAASK,UAAUA,CAACb,MAAmC,EAAE;EAAA,IAAAc,qBAAA,EAAAC,eAAA;EAC9D,QAAAD,qBAAA,IAAAC,eAAA,GAAOf,MAAM,CAACgB,OAAO,cAAAD,eAAA,uBAAdA,eAAA,CAAgBE,OAAO,cAAAH,qBAAA,cAAAA,qBAAA,GAAI,IAAI;AACxC;AAEA,SAASI,cAAcA,CAACN,WAAmB,EAAEO,IAAsB,EAAE;EACnE,OAAOC,eAAI,CAACC,IAAI,CAACT,WAAW,EAAE,SAAS,EAAE,KAAK,EAAE,KAAK,EAAEO,IAAI,EAAE,MAAM,CAAC;AACtE;AAEA,SAASG,qBAAqBA,CAACV,WAAmB,EAAEW,WAAmB,EAAE;EACvE,MAAMC,eAAe,GAAG,IAAAC,2BAAkB,EAACb,WAAW,EAAE,iBAAiB,CAAC;EAC1E,MAAMc,WAAW,GAAGN,eAAI,CAACO,OAAO,CAACH,eAAe,CAAC;EACjD,MAAMI,gBAAgB,GAAGR,eAAI,CAACS,QAAQ,CAACN,WAAW,EAAEG,WAAW,CAAC,CAACI,KAAK,CAACV,eAAI,CAACW,GAAG,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC;EAEhG,OAAOL,gBAAgB,CAACP,IAAI,CAAC,GAAG,CAAC;AACnC;AAEA,SAASa,+BAA+BA,CACtCtB,WAAmB,EACnBW,WAAmB,EACnBY,QAAgB,EAChBhB,IAAY,EACZ;EACA,MAAMiB,QAAQ,GAAG,IAAAC,YAAQ,EACvBjB,eAAI,CAACC,IAAI,CAACT,WAAW,EAAG,mBAAkBO,IAAK,YAAWgB,QAAS,aAAY,CAAC,CACjF,CAAC,CAAC,CAAC;EAEJ,IAAI,CAACC,QAAQ,EAAE;IACb,OAAO,IAAI;EACb;EAEA,MAAMV,WAAW,GAAGN,eAAI,CAACO,OAAO,CAACS,QAAQ,CAAC;EAC1C,MAAMR,gBAAgB,GAAGR,eAAI,CAACS,QAAQ,CAACN,WAAW,EAAEG,WAAW,CAAC,CAACI,KAAK,CAACV,eAAI,CAACW,GAAG,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC;EAEhG,OAAOL,gBAAgB,CAACP,IAAI,CAAC,GAAG,CAAC;AACnC;AAEA,SAASiB,4BAA4BA,CAAC1B,WAAmB,EAAEO,IAAY,EAAiB;EACtF,MAAMI,WAAW,GAAGL,cAAc,CAACN,WAAW,EAAEO,IAAI,CAAQ;EAE5D,IAAIA,IAAI,KAAK,MAAM,EAAE;IACnB,OAAOG,qBAAqB,CAACV,WAAW,EAAEW,WAAW,CAAC;EACxD;EACA;EACA,OAAOW,+BAA+B,CAACtB,WAAW,EAAEW,WAAW,EAAE,GAAG,EAAEJ,IAAI,CAAC;AAC7E;;AAEA;AACA;AACA;AACO,eAAeT,mBAAmBA,CACvCV,MAAmC,EACnCY,WAAmB,EACnB;EACA,MAAM2B,cAAc,GAAG1B,UAAU,CAACb,MAAM,CAAC;EACzC,IAAIuC,cAAc,KAAK,IAAI,EAAE;IAC3B;EACF;EAEA,KAAK,MAAMpB,IAAI,IAAI,CAAC,OAAO,EAAE,MAAM,EAAE,SAAS,CAAC,EAAE;IAC/C,MAAMqB,sBAAsB,CAAC;MAAE5B,WAAW;MAAEO,IAAI;MAAEsB,WAAW,EAAEF;IAAe,CAAC,CAAC;IAChF,MAAMG,0BAA0B,CAAC;MAAE9B,WAAW;MAAEO,IAAI;MAAEsB,WAAW,EAAEF;IAAe,CAAC,CAAC;EACtF;AACF;AAEO,eAAeC,sBAAsBA,CAAC;EAC3C5B,WAAW;EACXO,IAAI;EACJsB;AAKF,CAAC,EAAE;EACD,IAAI,CAACA,WAAW,EAAE;IAChB;EACF;EAEA,MAAME,kBAAkB,GAAGL,4BAA4B,CAAC1B,WAAW,EAAEO,IAAI,CAAC;EAC1E,IAAI,CAACwB,kBAAkB,IAAI,CAACF,WAAW,IAAIE,kBAAkB,KAAKF,WAAW,EAAE;IAC7E;EACF;EAEA,MAAMG,OAAO,GAAGxB,eAAI,CAACC,IAAI,CAACT,WAAW,EAAE,SAAS,EAAE,KAAK,EAAE,KAAK,EAAEO,IAAI,EAAE,KAAK,CAAC;EAC5E,MAAM0B,aAAa,GAAG,CAAC,GAAG,IAAAR,YAAQ,EAAC,MAAM,EAAE;IAAES,GAAG,EAAEF,OAAO;IAAEG,QAAQ,EAAE;EAAK,CAAC,CAAC,CAAC;EAC7E;EACAF,aAAa,CAACG,OAAO,CAAEC,QAAgB,IAAK;IAC1C,IAAI;MACF,IAAIC,aAAE,CAACC,SAAS,CAACF,QAAQ,CAAC,CAACG,MAAM,EAAE,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,CAACC,QAAQ,CAACjC,eAAI,CAACkC,OAAO,CAACL,QAAQ,CAAC,CAAC,EAAE;QACtF,IAAI7C,QAAQ,GAAG8C,aAAE,CAACK,YAAY,CAACN,QAAQ,CAAC,CAACO,QAAQ,EAAE;QACnDpD,QAAQ,GAAGA,QAAQ,CAACqD,OAAO,CACzB,IAAIC,MAAM,CAACC,4BAA4B,CAAChB,kBAAkB,CAAC,CAACc,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,EAAE,GAAG,CAAC,EACvFE,4BAA4B,CAAClB,WAAW,CAAC,CAC1C;QACDS,aAAE,CAACU,aAAa,CAACX,QAAQ,EAAE7C,QAAQ,CAAC;MACtC;IACF,CAAC,CAAC,MAAM;MACNP,KAAK,CAAE,mBAAkBoD,QAAS,eAAc9B,IAAK,GAAE,CAAC;IAC1D;EACF,CAAC,CAAC;AACJ;AAEO,eAAeuB,0BAA0BA,CAAC;EAC/C9B,WAAW;EACXO,IAAI;EACJsB;AAKF,CAAC,EAAE;EACD,IAAI,CAACA,WAAW,EAAE;IAChB;EACF;EAEA,MAAME,kBAAkB,GAAGL,4BAA4B,CAAC1B,WAAW,EAAEO,IAAI,CAAC;EAC1EtB,KAAK,CAAE,kBAAiB8C,kBAAmB,eAAcxB,IAAK,GAAE,CAAC;EACjE,IAAI,CAACwB,kBAAkB,IAAIA,kBAAkB,KAAKF,WAAW,EAAE;IAC7D;EACF;EACA5C,KAAK,CAAE,aAAY8C,kBAAmB,SAAQF,WAAY,eAActB,IAAK,GAAE,CAAC;EAChF,MAAMI,WAAW,GAAGL,cAAc,CAACN,WAAW,EAAEO,IAAI,CAAQ;EAC5D;EACA,IAAI,EAAE,MAAM,IAAA0C,+BAAoB,EAACtC,WAAW,CAAC,CAAC,EAAE;IAC9C1B,KAAK,CAAE,6CAA4C0B,WAAY,EAAC,CAAC;IACjE;EACF;EAEA,MAAMuC,kBAAkB,GAAG1C,eAAI,CAACC,IAAI,CAACE,WAAW,EAAE,GAAGoB,kBAAkB,CAACb,KAAK,CAAC,GAAG,CAAC,CAAC;EACnF,MAAMiC,cAAc,GAAG3C,eAAI,CAACC,IAAI,CAACE,WAAW,EAAE,GAAGkB,WAAW,CAACX,KAAK,CAAC,GAAG,CAAC,CAAC;;EAExE;EACAoB,aAAE,CAACc,SAAS,CAACD,cAAc,EAAE;IAAEE,SAAS,EAAE;EAAK,CAAC,CAAC;;EAEjD;EACA,IAAA5B,YAAQ,EAAC,MAAM,EAAE;IAAES,GAAG,EAAEgB;EAAmB,CAAC,CAAC,CAACd,OAAO,CAAEkB,YAAY,IAAK;IACtE,MAAMjB,QAAQ,GAAG7B,eAAI,CAACC,IAAI,CAACyC,kBAAkB,EAAEI,YAAY,CAAC;IAC5D,IAAIhB,aAAE,CAACC,SAAS,CAACF,QAAQ,CAAC,CAACG,MAAM,EAAE,EAAE;MACnCe,YAAY,CAAClB,QAAQ,EAAE7B,eAAI,CAACC,IAAI,CAAC0C,cAAc,EAAEG,YAAY,CAAC,CAAC;IACjE,CAAC,MAAM;MACLhB,aAAE,CAACc,SAAS,CAACf,QAAQ,EAAE;QAAEgB,SAAS,EAAE;MAAK,CAAC,CAAC;IAC7C;EACF,CAAC,CAAC;;EAEF;EACA;EACA,MAAMG,YAAY,GAAGzB,kBAAkB,CAACb,KAAK,CAAC,GAAG,CAAC;EAClD,OAAOsC,YAAY,CAACC,MAAM,EAAE;IAC1B,MAAMC,WAAW,GAAGlD,eAAI,CAACC,IAAI,CAACE,WAAW,EAAE,GAAG6C,YAAY,CAAC;IAC3D,IAAI;MACF,MAAMG,KAAK,GAAGrB,aAAE,CAACsB,WAAW,CAACF,WAAW,CAAC;MACzC,IAAIC,KAAK,CAACF,MAAM,KAAK,CAAC,EAAE;QACtBnB,aAAE,CAACuB,SAAS,CAACH,WAAW,CAAC;MAC3B;IACF,CAAC,SAAS;MACRF,YAAY,CAACM,GAAG,EAAE;IACpB;EACF;EAEA,MAAM7B,aAAa,GAAG,CAAC,GAAG,IAAAR,YAAQ,EAAC,MAAM,EAAE;IAAES,GAAG,EAAEiB,cAAc;IAAEhB,QAAQ,EAAE;EAAK,CAAC,CAAC,CAAC;EACpF;EACA,IAAI5B,IAAI,KAAK,MAAM,EAAE;IACnB;IACA0B,aAAa,CAAC8B,IAAI,CAACvD,eAAI,CAACC,IAAI,CAACT,WAAW,EAAE,SAAS,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;EACtE;EACA;EACAiC,aAAa,CAACG,OAAO,CAAEC,QAAgB,IAAK;IAC1C,IAAI;MACF,IAAIC,aAAE,CAACC,SAAS,CAACF,QAAQ,CAAC,CAACG,MAAM,EAAE,EAAE;QACnC,IAAIhD,QAAQ,GAAG8C,aAAE,CAACK,YAAY,CAACN,QAAQ,CAAC,CAACO,QAAQ,EAAE;QACnDpD,QAAQ,GAAGA,QAAQ,CAACqD,OAAO,CAAC,IAAIC,MAAM,CAACf,kBAAkB,EAAG,GAAG,CAAC,EAAEF,WAAW,CAAC;QAC9E,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,CAACY,QAAQ,CAACjC,eAAI,CAACkC,OAAO,CAACL,QAAQ,CAAC,CAAC,EAAE;UACnD7C,QAAQ,GAAGA,QAAQ,CAACqD,OAAO,CACzB,IAAIC,MAAM,CAACC,4BAA4B,CAAChB,kBAAkB,CAAC,CAACc,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,EAAE,GAAG,CAAC,EACtFE,4BAA4B,CAAClB,WAAW,CAAC,CAC1C;QACH;QACAS,aAAE,CAACU,aAAa,CAACX,QAAQ,EAAE7C,QAAQ,CAAC;MACtC;IACF,CAAC,CAAC,MAAM;MACNP,KAAK,CAAE,mBAAkBoD,QAAS,eAAc9B,IAAK,GAAE,CAAC;IAC1D;EACF,CAAC,CAAC;AACJ;AAEA,SAASgD,YAAYA,CAACS,GAAW,EAAEC,IAAY,EAAE;EAC/C3B,aAAE,CAACc,SAAS,CAAC5C,eAAI,CAACO,OAAO,CAACkD,IAAI,CAAC,EAAE;IAAEZ,SAAS,EAAE;EAAK,CAAC,CAAC;EACrDf,aAAE,CAAC4B,UAAU,CAACF,GAAG,EAAEC,IAAI,CAAC;AAC1B;AAEO,SAASxE,uBAAuBA,CAACL,MAAmC,EAAE+E,WAAmB,EAAE;EAChG,MAAMtC,WAAW,GAAG5B,UAAU,CAACb,MAAM,CAAC;EACtC,IAAIyC,WAAW,KAAK,IAAI,EAAE;IACxB,OAAOsC,WAAW;EACpB;EAEA,MAAMC,OAAO,GAAG,IAAItB,MAAM,CAAE,sCAAqC,EAAE,GAAG,CAAC;EACvE,OAAOqB,WAAW,CAACtB,OAAO,CAACuB,OAAO,EAAG,OAAMvC,WAAY,GAAE,CAAC;AAC5D;AAEO,eAAewC,qBAAqBA,CAACrE,WAAmB,EAA0B;EAAA,IAAAsE,aAAA;EACvF,MAAMC,eAAe,GAAG,IAAAC,kCAAyB,EAACxE,WAAW,CAAC;EAC9D,IAAI,CAACsC,aAAE,CAACmC,UAAU,CAACF,eAAe,CAAC,EAAE;IACnC,OAAO,IAAI;EACb;EACA,MAAMJ,WAAW,GAAG,MAAM7B,aAAE,CAACoC,QAAQ,CAACC,QAAQ,CAACJ,eAAe,EAAE,MAAM,CAAC;EACvE,MAAMK,WAAW,GAAGT,WAAW,CAACU,KAAK,CAAC,4BAA4B,CAAC;EACnE;EACA,QAAAP,aAAA,GAAOM,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAG,CAAC,CAAC,cAAAN,aAAA,cAAAA,aAAA,GAAI,IAAI;AACjC;;AAEA;AACA;AACA;AACA;AACA,SAASvB,4BAA4BA,CAAClB,WAAmB,EAAE;EACzD,OAAQ,IAAGA,WAAW,CAACgB,OAAO,CAAC,KAAK,EAAE,GAAG,CAAE,EAAC;AAC9C"}