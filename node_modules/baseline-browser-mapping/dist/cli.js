#!/usr/bin/env node
import{parseArgs as e}from"node:util";import{exit as a}from"node:process";import{getCompatibleVersions as s}from"./index.js";const n=process.argv.slice(2),{values:o}=e({args:n,options:{"target-year":{type:"string"},"widely-available-on-date":{type:"string"},"include-downstream-browsers":{type:"boolean"},"list-all-compatible-versions":{type:"boolean"},"include-kaios":{type:"boolean"},help:{type:"boolean",short:"h"}},strict:!0});o.help&&(console.log("\nGet Baseline Widely available browser versions or Baseline year browser versions.\n\nUsage: baseline-browser-mapping [options]\n\nOptions:\n      --target-year                   Pass a year between 2015 and the current year to get browser versions compatible \n                                      with all Newly Available features as of the end of the year specified.\n      --widely-available-on-date      Pass a date in the format 'YYYY-MM-DD' to get versions compatible with Widely \n                                      available on the specified date.\n      --include-downstream-browsers   Whether to include browsers that use the same engines as a core Baseline browser.\n      --include-kaios                 Whether to include KaiOS in downstream browsers.  Requires --include-downstream-browsers.\n      --list-all-compatible-versions  Whether to include only the minimum compatible browser versions or all compatible versions.\n  -h, --help                          Show help\n\nExamples:\n  npx baseline-browser-mapping --target-year 2020\n  npx baseline-browser-mapping --widely-available-on-date 2023-04-05\n  npx baseline-browser-mapping --include-downstream-browsers\n  npx baseline-browser-mapping --list-all-compatible-versions\n".trim()),a(0)),console.log(s({targetYear:o["target-year"]?Number.parseInt(o["target-year"]):void 0,widelyAvailableOnDate:o["widely-available-on-date"],includeDownstreamBrowsers:o["include-downstream-browsers"],listAllCompatibleVersions:o["list-all-compatible-versions"],includeKaiOS:o["include-kaios"]}));
