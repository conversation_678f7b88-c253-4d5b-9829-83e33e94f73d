declare const _default: typeof import("./index.js").glob & {
    glob: typeof import("./index.js").glob;
    globSync: typeof import("./index.js").globSync;
    sync: typeof import("./index.js").globSync & {
        stream: typeof import("./index.js").globStreamSync;
        iterate: typeof import("./index.js").globIterateSync;
    };
    globStream: typeof import("./index.js").globStream;
    stream: typeof import("./index.js").globStream & {
        sync: typeof import("./index.js").globStreamSync;
    };
    globStreamSync: typeof import("./index.js").globStreamSync;
    streamSync: typeof import("./index.js").globStreamSync;
    globIterate: typeof import("./index.js").globIterate;
    iterate: typeof import("./index.js").globIterate & {
        sync: typeof import("./index.js").globIterateSync;
    };
    globIterateSync: typeof import("./index.js").globIterateSync;
    iterateSync: typeof import("./index.js").globIterateSync;
    Glob: typeof import("./glob.js").Glob;
    hasMagic: (pattern: string | string[], options?: import("./glob.js").GlobOptions) => boolean;
    escape: (s: string, { windowsPathsNoEscape, }?: Pick<import("minimatch").MinimatchOptions, "windowsPathsNoEscape"> | undefined) => string;
    unescape: (s: string, { windowsPathsNoEscape, }?: Pick<import("minimatch").MinimatchOptions, "windowsPathsNoEscape"> | undefined) => string;
} & {
    default: typeof import("./index.js").glob & {
        glob: typeof import("./index.js").glob;
        globSync: typeof import("./index.js").globSync;
        sync: typeof import("./index.js").globSync & {
            stream: typeof import("./index.js").globStreamSync;
            iterate: typeof import("./index.js").globIterateSync;
        };
        globStream: typeof import("./index.js").globStream;
        stream: typeof import("./index.js").globStream & {
            sync: typeof import("./index.js").globStreamSync;
        };
        globStreamSync: typeof import("./index.js").globStreamSync;
        streamSync: typeof import("./index.js").globStreamSync;
        globIterate: typeof import("./index.js").globIterate;
        iterate: typeof import("./index.js").globIterate & {
            sync: typeof import("./index.js").globIterateSync;
        };
        globIterateSync: typeof import("./index.js").globIterateSync;
        iterateSync: typeof import("./index.js").globIterateSync;
        Glob: typeof import("./glob.js").Glob;
        hasMagic: (pattern: string | string[], options?: import("./glob.js").GlobOptions) => boolean;
        escape: (s: string, { windowsPathsNoEscape, }?: Pick<import("minimatch").MinimatchOptions, "windowsPathsNoEscape"> | undefined) => string;
        unescape: (s: string, { windowsPathsNoEscape, }?: Pick<import("minimatch").MinimatchOptions, "windowsPathsNoEscape"> | undefined) => string;
    };
    glob: typeof import("./index.js").glob & {
        glob: typeof import("./index.js").glob;
        globSync: typeof import("./index.js").globSync;
        sync: typeof import("./index.js").globSync & {
            stream: typeof import("./index.js").globStreamSync;
            iterate: typeof import("./index.js").globIterateSync;
        };
        globStream: typeof import("./index.js").globStream;
        stream: typeof import("./index.js").globStream & {
            sync: typeof import("./index.js").globStreamSync;
        };
        globStreamSync: typeof import("./index.js").globStreamSync;
        streamSync: typeof import("./index.js").globStreamSync;
        globIterate: typeof import("./index.js").globIterate;
        iterate: typeof import("./index.js").globIterate & {
            sync: typeof import("./index.js").globIterateSync;
        };
        globIterateSync: typeof import("./index.js").globIterateSync;
        iterateSync: typeof import("./index.js").globIterateSync;
        Glob: typeof import("./glob.js").Glob;
        hasMagic: (pattern: string | string[], options?: import("./glob.js").GlobOptions) => boolean;
        escape: (s: string, { windowsPathsNoEscape, }?: Pick<import("minimatch").MinimatchOptions, "windowsPathsNoEscape"> | undefined) => string;
        unescape: (s: string, { windowsPathsNoEscape, }?: Pick<import("minimatch").MinimatchOptions, "windowsPathsNoEscape"> | undefined) => string;
    };
};
export = _default;
//# sourceMappingURL=index-cjs.d.ts.map